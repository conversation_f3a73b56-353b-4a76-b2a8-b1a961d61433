// CrowdSnap - AI-Powered Event Management
// Enhanced JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Header scroll effect
    initializeHeaderScroll();

    // Smooth scrolling for navigation links
    initializeSmoothScrolling();

    // Dashboard animations
    initializeDashboardAnimations();

    // App screen interactions
    initializeAppScreens();

    // Intersection Observer for fade-in animations
    initializeScrollAnimations();

    // Partners section animations
    initializePartnersEffects();

    // Gallery interactions
    initializeGalleryEffects();

    // Gallery responsive behavior
    initializeGalleryResponsive();

    // Button ripple effects
    initializeButtonEffects();

    // Parallax effects
    initializeParallaxEffects();

    // Loading animation
    initializeLoadingAnimation();

    // Dropdown functionality
    initializeDropdowns();

    // Update copyright year
    updateCopyrightYear();
}

// Header scroll effect
function initializeHeaderScroll() {
    const header = document.querySelector('header');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(46, 55, 63, 0.98)';
            header.style.backdropFilter = 'blur(15px)';
            header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
        } else {
            header.style.background = 'rgba(46, 55, 63, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
            header.style.boxShadow = 'none';
        }
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Dashboard animations
function initializeDashboardAnimations() {
    const dashboardCards = document.querySelectorAll('.dashboard-card');

    // Add hover effects to dashboard cards
    dashboardCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Animate dashboard on load
    setTimeout(() => {
        const mainDashboard = document.querySelector('.main-dashboard');
        if (mainDashboard) {
            mainDashboard.style.animation = 'dashboardPulse 2s ease-in-out infinite alternate';
        }
    }, 1000);
}

// App screen interactions
function initializeAppScreens() {
    const appScreens = document.querySelectorAll('.app-screen');

    appScreens.forEach(screen => {
        // Add click interaction
        screen.addEventListener('click', function() {
            // Add a subtle click effect
            this.style.transform += ' scale(0.95)';
            setTimeout(() => {
                this.style.transform = this.style.transform.replace(' scale(0.95)', '');
            }, 150);
        });

        // Add random floating animation
        const randomDelay = Math.random() * 2000;
        setTimeout(() => {
            screen.style.animation = `floatScreen 4s ease-in-out infinite alternate`;
            screen.style.animationDelay = `${Math.random() * 2}s`;
        }, randomDelay);
    });
}

// Intersection Observer for fade-in animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all fade-in elements
    document.querySelectorAll('.fade-in').forEach(el => {
        observer.observe(el);
    });
}

// Partners section effects
function initializePartnersEffects() {
    const partnerCards = document.querySelectorAll('.partner-card');

    // Add staggered animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, observerOptions);

    // Initialize cards with hidden state
    partnerCards.forEach((card) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);

        // Add click effect
        card.addEventListener('click', function() {
            this.style.transform = 'translateY(-8px) scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-8px) scale(1)';
            }, 150);
        });
    });

    // Trophy animation enhancement
    const trophy = document.querySelector('.trophy-icon');
    if (trophy) {
        trophy.addEventListener('mouseenter', function() {
            this.style.animation = 'none';
            this.style.transform = 'scale(1.2) rotate(15deg)';
        });

        trophy.addEventListener('mouseleave', function() {
            this.style.animation = 'float 3s ease-in-out infinite';
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    }
}

// Enhanced Gallery Effects
function initializeGalleryEffects() {
    const galleryContainer = document.querySelector('.gallery-scroll-container');
    const galleryScroll = document.querySelector('.gallery-scroll');

    if (galleryContainer && galleryScroll) {
        // Store original items
        const originalItems = Array.from(galleryScroll.children);

        // Clear and rebuild with duplicated items for seamless loop
        galleryScroll.innerHTML = '';

        // Add original items
        originalItems.forEach(item => {
            galleryScroll.appendChild(item.cloneNode(true));
        });

        // Add duplicated items for seamless loop
        originalItems.forEach(item => {
            galleryScroll.appendChild(item.cloneNode(true));
        });

        // Calculate exact width for seamless loop
        const itemWidth = 350; // item width
        const gap = 30; // gap between items
        const totalItems = galleryScroll.children.length;
        const totalWidth = (itemWidth + gap) * totalItems - gap;
        galleryScroll.style.width = `${totalWidth}px`;

        // Pause on hover with smooth transition
        galleryContainer.addEventListener('mouseenter', () => {
            galleryScroll.style.animationPlayState = 'paused';
            galleryScroll.style.filter = 'brightness(1.1)';
        });

        galleryContainer.addEventListener('mouseleave', () => {
            galleryScroll.style.animationPlayState = 'running';
            galleryScroll.style.filter = 'brightness(1)';
        });

        // Add click handlers for gallery items
        const galleryItems = galleryScroll.querySelectorAll('.gallery-item');
        galleryItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                // Add click effect
                item.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    item.style.transform = '';
                }, 200);

                // You can add lightbox or modal functionality here
                console.log(`Gallery item ${index + 1} clicked`);
            });
        });
    }
}

// Gallery responsive behavior
function initializeGalleryResponsive() {
    const galleryScroll = document.querySelector('.gallery-scroll');

    if (galleryScroll) {
        // Adjust animation speed to match testimonials section
        function adjustGallerySpeed() {
            const screenWidth = window.innerWidth;
            let animationDuration = '30s'; // Same speed as testimonials section

            if (screenWidth < 768) {
                animationDuration = '25s'; // Slightly faster on mobile like testimonials
            } else if (screenWidth < 1024) {
                animationDuration = '28s'; // Medium speed on tablet
            }

            galleryScroll.style.animationDuration = animationDuration;
        }

        // Initial adjustment
        adjustGallerySpeed();

        // Adjust on window resize
        window.addEventListener('resize', adjustGallerySpeed);

        // Add touch support for mobile
        galleryScroll.addEventListener('touchstart', function() {
            this.style.animationPlayState = 'paused';
        });

        galleryScroll.addEventListener('touchend', function() {
            // Resume after a short delay to prevent immediate restart
            setTimeout(() => {
                this.style.animationPlayState = 'running';
            }, 500);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === ' ' || e.key === 'Spacebar') {
                e.preventDefault();
                const isPaused = galleryScroll.style.animationPlayState === 'paused';
                galleryScroll.style.animationPlayState = isPaused ? 'running' : 'paused';
            }
        });
    }
}

// Button ripple effects
function initializeButtonEffects() {
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255,255,255,0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Parallax effects
function initializeParallaxEffects() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');
        if (hero) {
            hero.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
}

// Loading animation
function initializeLoadingAnimation() {
    window.addEventListener('load', () => {
        document.body.style.opacity = '0';
        setTimeout(() => {
            document.body.style.transition = 'opacity 0.5s ease';
            document.body.style.opacity = '1';
        }, 100);
    });
}

// Add CSS animations dynamically
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes dashboardPulse {
            0% { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3); }
            100% { box-shadow: 0 30px 60px rgba(173, 217, 41, 0.2); }
        }

        @keyframes floatScreen {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-10px); }
        }

        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// Initialize dynamic styles
addDynamicStyles();

// Utility function for future features
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);
    // This can be expanded to show actual notifications in the UI
}

// Dropdown functionality for How It Works section
function initializeDropdowns() {
    // Make toggleDropdown function globally available
    window.toggleDropdown = function(header) {
        const dropdownStep = header.parentElement;
        const isActive = dropdownStep.classList.contains('active');

        // Close all other dropdowns
        document.querySelectorAll('.dropdown-step').forEach(step => {
            step.classList.remove('active');
        });

        // Toggle current dropdown
        if (!isActive) {
            dropdownStep.classList.add('active');
        }
    };
}

// Update copyright year automatically
function updateCopyrightYear() {
    const currentYear = new Date().getFullYear();
    const copyrightElements = document.querySelectorAll('.copyright-year');

    copyrightElements.forEach(element => {
        element.textContent = currentYear;
    });

    // Also update any element with copyright text
    const footerText = document.querySelector('footer p');
    if (footerText && footerText.textContent.includes('2024')) {
        footerText.innerHTML = footerText.innerHTML.replace('2024', `<span class="copyright-year">${currentYear}</span>`);
    }
}

// Export functions for potential future use
window.CrowdSnap = {
    showNotification,
    initializeApp,
    toggleDropdown: window.toggleDropdown
};
