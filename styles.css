/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #2e373f;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: rgba(46, 55, 63, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 28px;
    font-weight: bold;
}

.logo-crowd {
    color: white;
}

.logo-snap {
    color: #add929;
}

.logo-events {
    font-size: 0.4em;
    color: white;
    font-weight: normal;
    background-color: #3b4650;
    padding: 4px 8px;
    border-radius: 6px;
    margin-left: 4px;
    display: inline-block;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

nav a:hover {
    color: #add929;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #add929, #ffffff);
    transition: width 0.3s ease;
}

nav a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg,
        rgba(46, 55, 63, 0.9) 0%,
        rgba(0, 0, 0, 0.8) 50%,
        rgba(173, 217, 41, 0.2) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="gradient" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:%23add929;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%232e373f;stop-opacity:0.05"/></radialGradient></defs><circle cx="500" cy="500" r="500" fill="url(%23gradient)"/></svg>');
    background-size: cover;
    position: relative;
    overflow: hidden;
    padding-top: 120px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(173,217,41,0.2)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.15)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    z-index: 2;
    position: relative;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 300;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #2C9E4B 0%, #A0D405 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.hero-text .subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-main-image {
    width: 600px;
    height: 450px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
}

.hero-main-image:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
}



/* Main Dashboard Mockup */
.main-dashboard {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotateY(-8deg);
    width: 320px;
    height: 200px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: 5;
    overflow: hidden;
}

.dashboard-header {
    height: 40px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    display: flex;
    align-items: center;
    padding: 0 15px;
    gap: 8px;
}

.dashboard-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.dot-1 { background: #ff5f57; }
.dot-2 { background: #ffbd2e; }
.dot-3 { background: #28ca42; }

.dashboard-title {
    color: white;
    font-size: 11px;
    font-weight: 600;
    margin-left: 10px;
}

.dashboard-content {
    padding: 20px 15px;
    height: calc(100% - 40px);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.dashboard-card {
    background: linear-gradient(135deg, #add929 0%, rgba(173, 217, 41, 0.1) 100%);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #2e373f;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
}

.dashboard-card .icon {
    font-size: 18px;
    margin-bottom: 4px;
}

/* Floating App Screens */
.app-screen {
    position: absolute;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: all 0.4s ease;
}

.app-screen:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 30px 60px rgba(173, 217, 41, 0.3);
    border-color: rgba(173, 217, 41, 0.5);
}

.mobile-screen {
    width: 120px;
    height: 200px;
    top: 20px;
    right: 40px;
    transform: rotateY(15deg) rotateZ(-5deg);
    z-index: 4;
}

.tablet-screen {
    width: 180px;
    height: 130px;
    bottom: 40px;
    left: 20px;
    transform: rotateY(-12deg) rotateZ(3deg);
    z-index: 3;
}

.watch-screen {
    width: 80px;
    height: 80px;
    top: 60px;
    left: 60px;
    border-radius: 50%;
    transform: rotateY(20deg);
    z-index: 6;
}

/* Screen Content */
.screen-content {
    padding: 15px 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.mobile-screen .screen-content {
    padding: 20px 12px;
}

.screen-header {
    height: 25px;
    background: linear-gradient(135deg, #2e373f 0%, #add929 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
    font-weight: 600;
    margin-bottom: 8px;
}

.screen-body {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.mobile-screen .screen-body {
    grid-template-columns: 1fr;
    gap: 8px;
}

.screen-item {
    background: rgba(173, 217, 41, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #2e373f;
    min-height: 20px;
}

.mobile-screen .screen-item {
    min-height: 25px;
    font-size: 10px;
}

/* Analytics Cards */
.analytics-card {
    position: absolute;
    background: rgba(46, 55, 63, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 15px;
    color: white;
    border: 1px solid rgba(173, 217, 41, 0.3);
    transition: all 0.4s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
    border-color: #add929;
    box-shadow: 0 15px 30px rgba(173, 217, 41, 0.2);
}

.chart-card {
    width: 140px;
    height: 90px;
    top: 30px;
    right: 180px;
    transform: rotateY(-10deg);
    z-index: 2;
}

.stats-card {
    width: 120px;
    height: 70px;
    bottom: 80px;
    right: 200px;
    transform: rotateY(8deg);
    z-index: 2;
}

.card-title {
    font-size: 10px;
    color: #add929;
    margin-bottom: 8px;
    font-weight: 600;
}

.card-content {
    font-size: 8px;
    line-height: 1.3;
    opacity: 0.9;
}

.chart-visual {
    width: 100%;
    height: 40px;
    background: linear-gradient(135deg, rgba(173, 217, 41, 0.3) 0%, rgba(173, 217, 41, 0.1) 100%);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.chart-visual::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60%;
    height: 70%;
    background: linear-gradient(135deg, #add929 0%, rgba(173, 217, 41, 0.7) 100%);
    border-radius: 2px 2px 0 0;
}

/* QR Code Element */
.qr-element {
    position: absolute;
    width: 60px;
    height: 60px;
    bottom: 180px;
    left: 180px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    z-index: 7;
    border: 2px solid #add929;
}

/* Floating Particles */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(173, 217, 41, 0.6);
    border-radius: 50%;
}

.particle-1 { top: 10%; left: 20%; }
.particle-2 { top: 80%; right: 15%; }
.particle-3 { top: 40%; left: 10%; }
.particle-4 { bottom: 20%; right: 30%; }

/* CTA Buttons */
.cta-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #add929 0%, #2e373f 100%);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Platform Section */
.platform-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 100px 0;
    position: relative;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 60px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.platform-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.platform-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2e373f;
    order: 2;
}

.platform-image {
    order: 1;
    position: relative;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    transition: all 0.4s ease;
}

.image-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 35px 70px rgba(173, 217, 41, 0.2);
}

.image-container img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.image-container:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(46, 55, 63, 0.8) 0%,
        rgba(0, 0, 0, 0.6) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
    padding: 20px;
}

.overlay-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #add929;
}

.overlay-content p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Add decorative elements */
.image-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #add929, transparent, #add929);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.image-container:hover::before {
    opacity: 0.6;
}

@keyframes rotate {
    100% { transform: rotate(360deg); }
}

/* Features Section */
.features-section {
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    padding: 100px 0;
    color: white;
}

.features-section .section-title {
    color: #ffffff !important;
    background: transparent !important;
    background-image: none !important;
    -webkit-background-clip: border-box !important;
    -webkit-text-fill-color: #ffffff !important;
    background-clip: border-box !important;
    text-shadow: none !important;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.feature-card {
    background: linear-gradient(135deg,
        rgba(173, 217, 41, 0.1) 0%,
        rgba(46, 55, 63, 0.1) 100%);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(173, 217, 41, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: #add929;
}

.feature-image {
    position: relative;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
    height: 200px;
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.feature-card:hover .feature-image img {
    transform: scale(1.1);
}

.feature-content {
    padding: 25px;
    flex: 1;
    position: relative;
    z-index: 2;
}

.feature-content h3 {
    color: #add929;
    margin-bottom: 15px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.feature-content p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.arrow {
    color: #add929;
    font-size: 1.5rem;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.feature-card:hover .arrow {
    transform: translateX(5px);
}

/* Partners Section */
.partners-section {
    background: #ffffff;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.partners-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle 80px at 12% 18%, rgba(173, 217, 41, 0.15) 0%, transparent 70%),
        radial-gradient(circle 120px at 88% 25%, rgba(124, 179, 66, 0.12) 0%, transparent 70%),
        radial-gradient(circle 60px at 25% 65%, rgba(173, 217, 41, 0.18) 0%, transparent 70%),
        radial-gradient(circle 100px at 78% 82%, rgba(124, 179, 66, 0.14) 0%, transparent 70%),
        radial-gradient(circle 40px at 45% 15%, rgba(173, 217, 41, 0.20) 0%, transparent 70%),
        radial-gradient(circle 90px at 65% 45%, rgba(124, 179, 66, 0.10) 0%, transparent 70%),
        radial-gradient(circle 50px at 15% 88%, rgba(173, 217, 41, 0.16) 0%, transparent 70%),
        radial-gradient(circle 70px at 92% 60%, rgba(124, 179, 66, 0.13) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.partners-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle 35px at 35% 35%, rgba(173, 217, 41, 0.08) 0%, transparent 70%),
        radial-gradient(circle 55px at 72% 15%, rgba(124, 179, 66, 0.09) 0%, transparent 70%),
        radial-gradient(circle 45px at 8% 55%, rgba(173, 217, 41, 0.11) 0%, transparent 70%),
        radial-gradient(circle 65px at 58% 78%, rgba(124, 179, 66, 0.07) 0%, transparent 70%),
        radial-gradient(circle 30px at 82% 35%, rgba(173, 217, 41, 0.12) 0%, transparent 70%),
        radial-gradient(circle 75px at 28% 8%, rgba(124, 179, 66, 0.06) 0%, transparent 70%);
    pointer-events: none;
    animation: floatCircles 30s ease-in-out infinite;
    z-index: 1;
}

@keyframes floatCircles {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) scale(1.05);
        opacity: 0.8;
    }
}

.partners-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.partners-text {
    flex: 1;
    position: relative;
    z-index: 2;
}

.partners-text .section-title {
    text-align: left;
    margin-bottom: 20px;
    font-size: 2.8rem;
    background: linear-gradient(135deg, #2e373f 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.partners-description {
    font-size: 1.2rem;
    color: #000;
    font-weight: bold;
    line-height: 1.6;
    max-width: 500px;
}

.partners-decoration {
    position: relative;
    margin-left: 40px;
}

.trophy-icon {
    font-size: 2rem;
    color: #add929;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(173, 217, 41, 0.3));
}

.trophy-icon img {
    width: 40px;
    height: 40px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
    margin-top: 40px;
    position: relative;
    z-index: 2;
}

.partner-card {
    background: transparent;
    border-radius: 0;
    padding: 20px 10px;
    box-shadow: none;
    border: none;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
}

.partner-card::before {
    display: none;
}

.partner-card:hover::before {
    display: none;
}

.partner-card:hover {
    transform: translateY(-5px);
    box-shadow: none;
    border-color: transparent;
}

.partner-logo {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.partner-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    filter: none;
    opacity: 1;
    transition: all 0.4s ease;
}

.partner-card:hover .partner-logo img {
    transform: scale(1.1);
    filter: brightness(1.1) contrast(1.1);
}

/* Responsive Partners Section */
@media (max-width: 1024px) {
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
    }

    .partners-content {
        flex-direction: column;
        text-align: center;
        margin-bottom: 40px;
    }

    .partners-decoration {
        margin-left: 0;
        margin-top: 20px;
    }

    .partners-text .section-title {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .partner-card {
        padding: 15px 10px;
        min-height: 100px;
    }

    .partner-logo {
        height: 85px;
    }

    .partners-text .section-title {
        font-size: 2.2rem;
    }

    .partners-description {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .partners-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .partner-card {
        padding: 25px 20px;
    }
}

/* Gallery Section - Enhanced Horizontal Scroll */
.gallery-section {
    background: #ffffff;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.gallery-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle 80px at 12% 18%, rgba(173, 217, 41, 0.15) 0%, transparent 70%),
        radial-gradient(circle 120px at 88% 25%, rgba(124, 179, 66, 0.12) 0%, transparent 70%),
        radial-gradient(circle 60px at 25% 65%, rgba(173, 217, 41, 0.18) 0%, transparent 70%),
        radial-gradient(circle 100px at 78% 82%, rgba(124, 179, 66, 0.14) 0%, transparent 70%),
        radial-gradient(circle 40px at 45% 15%, rgba(173, 217, 41, 0.20) 0%, transparent 70%),
        radial-gradient(circle 90px at 65% 45%, rgba(124, 179, 66, 0.10) 0%, transparent 70%),
        radial-gradient(circle 50px at 15% 88%, rgba(173, 217, 41, 0.16) 0%, transparent 70%),
        radial-gradient(circle 70px at 92% 60%, rgba(124, 179, 66, 0.13) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.gallery-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle 35px at 35% 35%, rgba(173, 217, 41, 0.08) 0%, transparent 70%),
        radial-gradient(circle 55px at 72% 15%, rgba(124, 179, 66, 0.09) 0%, transparent 70%),
        radial-gradient(circle 45px at 8% 55%, rgba(173, 217, 41, 0.11) 0%, transparent 70%),
        radial-gradient(circle 65px at 58% 78%, rgba(124, 179, 66, 0.07) 0%, transparent 70%),
        radial-gradient(circle 30px at 82% 35%, rgba(173, 217, 41, 0.12) 0%, transparent 70%),
        radial-gradient(circle 75px at 28% 8%, rgba(124, 179, 66, 0.06) 0%, transparent 70%);
    pointer-events: none;
    animation: floatGalleryCircles 25s ease-in-out infinite;
    z-index: 1;
}

@keyframes floatGalleryCircles {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-8px) scale(1.03);
        opacity: 0.9;
    }
}

.gallery-section .section-title {
    position: relative;
    z-index: 2;
}

/* How It Works Section */
.how-it-works-section {
    background: linear-gradient(135deg, #f8fdf4 0%, #f1f8e9 25%, #e8f5e8 50%, #dcedc8 75%, #c5e1a5 100%);
    padding: 80px 0;
    margin: 80px 0;
    border-radius: 24px;
    position: relative;
    overflow: hidden;
}

.how-it-works-section::before {
    content: '';
    position: absolute;
    top: -20%;
    left: -10%;
    width: 250px;
    height: 250px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.4) 0%, rgba(173, 217, 41, 0.3) 100%);
    border-radius: 50%;
    transform: rotate(-15deg);
    animation: float1 8s ease-in-out infinite;
}

.how-it-works-section::after {
    content: '';
    position: absolute;
    top: 60%;
    right: -5%;
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, rgba(173, 217, 41, 0.4) 0%, rgba(255, 255, 255, 0.3) 100%);
    border-radius: 50%;
    transform: rotate(25deg);
    animation: float2 10s ease-in-out infinite reverse;
}

/* Additional floating shapes */
.how-it-works-section .how-it-works-content::before {
    content: '';
    position: absolute;
    top: 20%;
    right: 15%;
    width: 135px;
    height: 135px;
    background: linear-gradient(60deg, rgba(173, 217, 41, 0.25) 0%, rgba(255, 255, 255, 0.3) 100%);
    border-radius: 50%;
    transform: rotate(45deg);
    animation: float3 12s ease-in-out infinite;
    z-index: 1;
}

.how-it-works-section .how-it-works-content::after {
    content: '';
    position: absolute;
    bottom: 10%;
    left: 10%;
    width: 160px;
    height: 160px;
    background: linear-gradient(120deg, rgba(173, 217, 41, 0.3) 0%, rgba(255, 255, 255, 0.25) 100%);
    border-radius: 50%;
    transform: rotate(-30deg);
    animation: float4 9s ease-in-out infinite;
    z-index: 1;
}

@keyframes float1 {
    0%, 100% { transform: rotate(-15deg) translateY(0px) scale(1); }
    50% { transform: rotate(-10deg) translateY(-20px) scale(1.05); }
}

@keyframes float2 {
    0%, 100% { transform: rotate(25deg) translateY(0px) scale(1); }
    50% { transform: rotate(30deg) translateY(-15px) scale(0.95); }
}

@keyframes float3 {
    0%, 100% { transform: rotate(45deg) translateY(0px) scale(1); }
    50% { transform: rotate(50deg) translateY(-10px) scale(1.1); }
}

@keyframes float4 {
    0%, 100% { transform: rotate(-30deg) translateY(0px) scale(1); }
    50% { transform: rotate(-25deg) translateY(-25px) scale(0.9); }
}

.how-it-works-content {
    display: flex;
    gap: 60px;
    max-width: 1400px;
    margin: 0 auto;
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

.how-it-works-text {
    flex: 1;
    color: white;
    padding-right: 40px;
}

.how-it-works-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
    color: #2e373f;
}

.how-it-works-text > p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 1;
    color: #2e373f;
}

.dropdown-steps-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dropdown-step {
    background: linear-gradient(135deg, #2e373f 0%, #add929 100%);
    border-radius: 25px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #2e373f 0%, #add929 100%);
}

.dropdown-header:hover {
    background: #add929;
}

.dropdown-header span {
    font-weight: 600;
    font-size: 1rem;
    color: white;
}

.dropdown-arrow {
    color: white;
    transition: transform 0.3s ease;
}

.dropdown-step.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
}

.dropdown-step.active .dropdown-content {
    max-height: 200px;
}

.dropdown-content p {
    padding: 20px 25px;
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.6;
    color: #333;
}

/* Process Flow Diagram */
.how-it-works-diagram {
    flex: 0.6;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 40px 20px;
}

.process-flow-vertical {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-height: 400px;
    justify-content: center;
}

.flow-step-vertical {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #2e373f;
    transition: all 0.3s ease;
    padding: 15px;
    border-radius: 12px;
    min-width: 100px;
}

.flow-step-vertical:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.flow-icon-vertical {
    background: white;
    color: #7cb342;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 5px;
}

.flow-step-vertical span {
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    color: #2e373f;
}

.flow-arrow-vertical {
    color: #2e373f;
    font-size: 1.5rem;
    font-weight: bold;
    opacity: 0.8;
    margin: -15px 0;
}

/* Responsive design for how it works */
@media (max-width: 1024px) {
    .how-it-works-content {
        flex-direction: column;
        gap: 40px;
    }

    .how-it-works-text {
        padding-right: 0;
    }

    .how-it-works-diagram {
        align-items: center;
        padding: 20px;
    }

    .process-flow-vertical {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
        min-height: auto;
        padding: 20px 15px;
    }

    .flow-arrow-vertical {
        transform: rotate(-90deg);
        margin: 0 -10px;
    }
}

@media (max-width: 768px) {
    .how-it-works-text h2 {
        font-size: 2rem;
    }

    .dropdown-step {
        margin: 0 10px;
    }

    .dropdown-header {
        padding: 15px 20px;
    }

    .dropdown-header span {
        font-size: 0.9rem;
    }

    .process-flow-vertical {
        flex-direction: column;
        gap: 15px;
        padding: 20px 10px;
    }

    .flow-arrow-vertical {
        transform: rotate(0deg);
        margin: -5px 0;
    }

    .flow-step-vertical {
        min-width: auto;
        padding: 10px;
    }

    .flow-icon-vertical {
        width: 45px;
        height: 45px;
    }
}

.gallery-scroll-container {
    overflow: hidden;
    margin-top: 40px;
    position: relative;
    width: 100%;
    white-space: nowrap;
    mask: linear-gradient(90deg, transparent, white 2%, white 98%, transparent);
    -webkit-mask: linear-gradient(90deg, transparent, white 2%, white 98%, transparent);
    z-index: 2;
}

.gallery-scroll {
    display: flex;
    gap: 15px;
    animation: scroll-infinite 35s linear infinite;
    will-change: transform;
}

@keyframes scroll-infinite {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50%));
    }
}

.gallery-item {
    flex: 0 0 380px;
    height: 320px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
    border: none;
}

.gallery-item.featured {
    flex: 0 0 380px;
    height: 320px;
    border: none;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
    filter: brightness(0.95) contrast(1.1) saturate(1.1);
}

.gallery-item:hover img {
    transform: scale(1.1);
    filter: brightness(1.1) contrast(1.2) saturate(1.2);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
    color: white;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.4s ease;
    z-index: 2;
}

.gallery-overlay h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
    letter-spacing: 0.5px;
}

.badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #add929;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    z-index: 3;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.badge.new {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.badge.trending {
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.gallery-item:hover {
    transform: scale(1.05) translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
    z-index: 10;
}

.gallery-item.featured:hover {
    transform: scale(1.08) translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

/* Gallery scroll animation */
@keyframes galleryScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Pause on hover */
.gallery-scroll-container:hover .gallery-scroll {
    animation-play-state: paused;
}

/* Mobile responsive gallery - maintaining consistent size */
@media (max-width: 768px) {
    .gallery-item {
        flex: 0 0 300px;
        height: 280px;
    }

    .gallery-item.featured {
        flex: 0 0 300px;
        height: 280px;
    }

    .gallery-scroll {
        gap: 12px;
        animation-duration: 32s;
    }

    .gallery-overlay h4 {
        font-size: 0.9rem;
    }

    .gallery-overlay {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .gallery-item {
        flex: 0 0 280px;
        height: 260px;
    }

    .gallery-item.featured {
        flex: 0 0 280px;
        height: 260px;
    }

    .gallery-scroll {
        gap: 10px;
        animation-duration: 28s;
    }

    .gallery-overlay h4 {
        font-size: 0.85rem;
    }

    .gallery-overlay {
        padding: 12px;
    }
}

/* Testimonials */
.testimonials-section {
    background: #f8f9fa;
    padding: 100px 0;
    color: #333;
}

.testimonials-section .section-title {
    color: #2e373f !important;
    background: transparent !important;
    background-image: none !important;
    -webkit-background-clip: border-box !important;
    -webkit-text-fill-color: #2e373f !important;
    background-clip: border-box !important;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.testimonial-card {
    flex: 0 0 350px;
    background: white;
    padding: 40px 30px 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all 0.3s ease;
    height: 280px;
    display: flex;
    flex-direction: column;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.quote-icon {
    font-size: 2.5rem;
    color: #add929;
    font-family: 'Times New Roman', serif;
    line-height: 1;
    margin-bottom: 5px;
    font-weight: bold;
}

.testimonial-text {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #333;
    margin-bottom: auto;
    font-style: italic;
    flex: 1;
}

.testimonial-author {
    margin-top: 20px;
}

.testimonial-author h4 {
    color: #2e373f;
    margin-bottom: 5px;
    font-size: 1.1rem;
    font-weight: 600;
}

.author-role {
    color: #666;
    font-size: 0.9rem;
    font-weight: 400;
}



/* Mobile responsive testimonials */
@media (max-width: 768px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
    }

    .testimonial-card {
        padding: 30px 20px 20px;
    }

    .quote-icon {
        font-size: 2.5rem;
    }

    .testimonial-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }
}

/* Footer */
footer {
    background: linear-gradient(135deg, #000000 0%, #2e373f 100%);
    padding: 60px 0 30px;
    color: white;
    text-align: center;
}

.footer-cta {
    margin-bottom: 40px;
}

.footer-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content,
    .platform-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .platform-image,
    .platform-text {
        order: unset;
    }

    .image-container img {
        height: 300px;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-main-image {
        width: 450px;
        height: 350px;
    }

    .main-dashboard {
        width: 280px;
        height: 160px;
    }

    .mobile-screen {
        width: 100px;
        height: 160px;
        top: 15px;
        right: 30px;
    }

    .tablet-screen {
        width: 140px;
        height: 100px;
        bottom: 30px;
        left: 15px;
    }

    .watch-screen {
        width: 60px;
        height: 60px;
        top: 50px;
        left: 50px;
    }

    .chart-card {
        width: 120px;
        height: 75px;
        top: 25px;
        right: 140px;
    }

    .stats-card {
        width: 100px;
        height: 60px;
        bottom: 70px;
        right: 160px;
    }

    .qr-element {
        width: 50px;
        height: 50px;
        bottom: 140px;
        left: 140px;
        font-size: 24px;
    }

    nav ul {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

details a {
    color: white;
    text-decoration: underline;
}

details a:hover {
    color: #add929;
}
